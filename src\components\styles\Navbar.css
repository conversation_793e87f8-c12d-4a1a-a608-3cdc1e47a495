.header {
  display: flex;
  max-width: var(--cMaxWidth);
  width: var(--cWidth);
  justify-content: space-between;
  padding: 20px 0px;
  margin-bottom: -100px;
  box-sizing: border-box;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  top: 0;
  z-index: 9999;
}
.header ul {
  font-size: 12px;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  list-style: none;
  column-gap: 40px;
  row-gap: 8px;
  align-items: end;
}
.header ul li {
  margin-left: 0px;
  letter-spacing: 1px;
  color: #ccc;
  font-weight: 600;
  cursor: pointer;
}
.navbar-connect {
  position: absolute;
  display: none;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 15px;
  letter-spacing: 1px;
  font-weight: 500;
}
.navbar-title {
  font-weight: 700;
  font-size: 14px;
  letter-spacing: 0.2px;
}
@media only screen and (min-width: 500px) {
  .header {
    padding: 20px 0px;
  }
  .header ul {
    flex-direction: row;
    align-items: center;
    font-size: 14px;
  }
  .header ul li {
    color: #eae5ec;
  }
  .navbar-title {
    font-size: 16px;
  }
}
@media only screen and (min-width: 900px) {
  .navbar-connect {
    display: block;
  }
}
@media only screen and (min-width: 1200px) {
  .header {
    padding: 35px 0px;
  }
  .header ul {
    column-gap: 80px;
    font-size: 16px;
  }
  .navbar-connect {
    font-size: 16px;
  }
  .navbar-title {
    font-size: 18px;
  }
}
