.contact-section {
  margin: auto;
  padding-bottom: 100px;
  margin-top: 100px;
}
.contact-section h3 {
  font-size: 60px;
  font-weight: 400;
  text-transform: uppercase;
  margin: 0;
}
.contact-flex {
  display: flex;
  justify-content: space-between;
}
.contact-flex h4 {
  font-weight: 500;
  margin: 0;
  opacity: 0.6;
}
.contact-box {
  display: flex;
  flex-direction: column;
}
.contact-flex p {
  margin-top: 10px;
  margin-bottom: 20px;
}
a.contact-social {
  font-size: 25px;
  border-bottom: 1px solid #ccc;
}
.contact-box h2 {
  font-weight: 400;
  font-size: 23px;
  margin: 0;
}
.contact-box h2 > span {
  color: var(--accentColor);
}
.contact-box h5 {
  font-size: 20px;
  font-weight: 500;
  line-height: 20px;
  display: flex;
  gap: 10px;
  opacity: 0.5;
}
@media only screen and (max-width: 1600px) {
  .contact-section h3 {
    font-size: 50px;
  }
  .contact-box h2 {
    font-size: 20px;
  }
  a.contact-social {
    font-size: 22px;
  }
}
@media only screen and (max-width: 1300px) {
  .contact-section h3 {
    font-size: 40px;
  }
  .contact-box h2 {
    font-size: 18px;
  }
  a.contact-social {
    font-size: 20px;
  }
  .contact-flex p {
    margin-top: 0px;
  }
}
@media only screen and (max-width: 900px) {
  .contact-flex {
    flex-direction: column;
    gap: 40px;
  }
  .contact-flex p {
    margin-bottom: 0px;
  }
  .contact-flex h4 {
    margin-top: 20px;
  }
  .contact-section {
    margin-top: 50px;
    padding-bottom: 50px;
  }
  .contact-container {
    width: calc(100% - 25px);
  }
}
