.work-section h2 {
  margin-top: 100px;
  font-size: 70px;
  font-weight: 500;
}
.work-section h2 > span {
  color: var(--accentColor);
}
.work-section {
  transition: 0s;
  height: 100vh;
  box-sizing: border-box;
  will-change: transform;
}
.work-container {
  margin: auto;
  display: flex;
  flex-direction: column;
  height: 100%;
  align-content: stretch;
}
.work-flex {
  width: 100%;
  display: flex;
  height: 100%;
  margin-left: -80px;
  padding-right: 120px;
  position: relative;
}
.work-box {
  padding: 80px;
  display: flex;
  flex-direction: column;
  width: 600px;
  box-sizing: border-box;
  border-right: 1px solid #363636;
  flex-shrink: 0;
  gap: 50px;
  justify-content: start;
}
.work-flex .work-box:nth-child(even) {
  flex-direction: column-reverse;
}
.work-flex::before,
.work-flex::after {
  content: "";
  width: calc(50000vw);
  height: 1px;
  background-color: #363636;
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}
.work-flex::after {
  top: 100%;
}
.work-title {
  justify-content: space-between;
  display: flex;
  width: 100%;
  margin-bottom: 20px;
}
.work-title > div {
  text-align: right;
}
.work-title h3 {
  font-size: 50px;
  line-height: 50px;
  margin: 0;
  font-weight: 600;
}

.work-info h4 {
  font-size: 18px;
  font-weight: 400;
  margin: 0;
}
.work-info p {
  font-weight: 200;
  color: #adacac;
  margin: 0;
  margin-top: 3px;
}
.work-info > p {
  width: 90%;
}
.work-image {
  display: flex;
  width: 100%;
  justify-content: center;
}
.work-image-in {
  position: relative;
}
.work-link {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: var(--backgroundColor);
  width: 50px;
  border-radius: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 25px;
  box-shadow: 0px 0px 10px 0px rgba(255, 255, 255, 0.5),
    inset 0px 0px 10px 0px #393939;
  transition: 0.3s;
  opacity: 0;
}
.work-image a:hover {
  color: inherit;
}
.work-image a:hover .work-link {
  opacity: 1;
}
.work-image video {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: #000;
  object-fit: cover;
}
.work-image img {
  max-width: 100%;
  max-height: 350px;
}
@media only screen and (max-height: 900px) {
  .work-image img {
    max-height: 250px;
  }
  .work-box {
    padding-top: 40px;
    padding-bottom: 40px;
  }
  .work-section h2 {
    font-size: 60px;
    margin-bottom: 30px;
    margin-top: 70px;
  }
}
@media only screen and (max-width: 1400px) {
  .work-title h3 {
    font-size: 35px;
  }
  .work-info p {
    font-size: 13px;
  }
  .work-info h4 {
    font-size: 15px;
  }
  .work-box {
    width: 450px;
    padding: 50px;
  }
  .work-flex {
    margin-left: -50px;
    padding-right: 75px;
  }
  .work-section h2 {
    font-size: 50px;
  }
}
@media only screen and (max-width: 1400px) {
  .work-box {
    width: 350px;
    padding: 30px;
  }
  .work-flex {
    margin-left: -30px;
    padding-right: 45px;
  }
}

@media only screen and (max-height: 650px) {
  .work-image img {
    max-height: 200px;
  }
  .work-section h2 {
    font-size: 40px;
    margin-bottom: 20px;
  }
  .work-box {
    gap: 20px;
  }
}
/* @media only screen and (max-width: 900px) {
  .work-image img {
    max-height: 200px;
  }
  .work-section h2 {
    font-size: 40px;
    margin-bottom: 20px;
  }
  .work-box {
    gap: 20px;
  }
} */
@media only screen and (max-width: 1025px) {
  .work-container {
    align-content: normal;
  }
  .work-flex {
    height: auto;
  }
}
