.section-container {
  width: 1300px;
}
.title,
.para {
  font-kerning: none;
  -webkit-text-rendering: optimizeSpeed;
  text-rendering: optimizeSpeed;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}
@media only screen and (max-width: 1600px) {
  .section-container {
    width: 1200px;
    max-width: calc(100% - 160px);
  }
}
@media only screen and (max-width: 1400px) {
  .section-container {
    width: 900px;
  }
}
@media only screen and (max-width: 900px) {
  .section-container {
    width: 500px;
    max-width: var(--cWidth);
  }
}
