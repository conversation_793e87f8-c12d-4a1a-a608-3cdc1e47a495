.icons-section {
  position: fixed;
  max-width: var(--cMaxWidth);
  width: var(--cWidth);
  bottom: 0;
  z-index: 99;
  left: 50%;
  transform: translateX(-50%);
}
.social-icons {
  position: absolute;
  left: -20px;
  bottom: 20px;
  display: none;
  flex-direction: column;
  gap: 8px;
  z-index: 999;
  padding: 10px;
}
.social-icons:hover {
  transition: 0.3s;
  color: var(--backgroundColor);
}
.social-icons a:hover {
  color: var(--backgroundColor);
  /* transform: scale(1.2); */
}
.social-icons span {
  width: 50px;
  height: 50px;
  position: relative;
  display: flex;
}
.social-icons a {
  --siLeft: 50%;
  --siTop: 50%;
  position: absolute;
  left: var(--siLeft, 50%);
  top: var(--siTop, 50%);
  transform: translate(-50%, -50%);
  display: flex;
  font-size: 23px;
  will-change: left, top;
  transition: transform 0.3s ease-out;
}
.resume-button {
  position: absolute;
  z-index: 99;
  display: flex;
  gap: 5px;
  bottom: 40px;
  right: 0;
  width: auto;
  text-wrap: nowrap;
  letter-spacing: 4px;
  font-size: 15px;
  line-height: 15px;
  font-weight: 600;
  color: #5e5e5e;
  cursor: pointer;
  transition: 0.5s;
  transform-origin: left bottom;
  transform: translateX(100%) rotateZ(-90deg);
}
.resume-button:hover {
  color: #fff;
}
div.resume-button span {
  color: #fff;
  font-size: 17px;
  margin-top: -1px;
  display: flex;
  align-items: center;
}
.check-line {
  position: fixed;
  top: 655px;
  left: 0;
  height: 1px;
  background-color: #ffffff;
  width: 100%;
  z-index: 99999;
}
@media only screen and (min-width: 900px) {
  .social-icons {
    display: flex;
    gap: 20px;
  }
  .social-icons a {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) {
  .resume-button {
    transform: none;
    font-size: 20px;
    line-height: 20px;
  }
  div.resume-button span {
    font-size: 23px;
    margin-top: -1.5px;
  }
}
