.whatIDO {
  display: flex;
  align-items: center;
  justify-content: center;
  place-items: center;
  position: relative;
  opacity: 1;
  height: 100vh;
  width: var(--cWidth);
  max-width: 1920px;
  margin: auto;
  z-index: 9;
}
.what-box {
  width: 50%;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 9;
}
.what-box h2 {
  font-size: calc(4vw + 25px);
  line-height: calc(4vw + 20px);
  font-weight: 600;
  margin-right: 10%;
  margin-bottom: 100px;
}

.hat-h2 {
  font-style: italic;
}
.do-h2 {
  color: var(--accentColor);
}
.what-box-in {
  flex-direction: column;
  height: 500px;
  margin-left: 200px;
  position: relative;
  display: none;
}
.what-content {
  width: 450px;
  height: 33%;
  min-height: 50%;
  transition: 0.5s;
  /* border: 0.5px dashed rgba(255, 255, 255, 0.3); */
  position: relative;
  padding: 50px;
  box-sizing: border-box;
}

.what-noTouch:hover,
.what-content-active {
  min-height: 67%;
  padding: 40px 50px;
}

.what-noTouch:hover ~ .what-content,
.what-box-in:hover .what-noTouch:not(:hover),
.what-content.what-sibling {
  min-height: 33%;
  padding: 10px 50px;
}
.what-content h3 {
  font-size: 35px;
  letter-spacing: 1px;
  margin: 0;
}
.what-content p {
  font-size: 14px;
  line-height: 18px;
  font-weight: 200;
  letter-spacing: 0.7px;
}
.what-content h4 {
  font-weight: 300;
  letter-spacing: 1px;
  margin: 0px;
  font-size: 14px;
  opacity: 0.3;
}
.what-content-in {
  opacity: 0;
  animation: whatFlicker 0.5s 1 forwards;
  animation-delay: 1s;
}
@keyframes whatFlicker {
  0%,
  25%,
  35%,
  60% {
    opacity: 0;
  }

  30%,
  50%,
  40%,
  100% {
    opacity: 1;
  }
}
.what-content::before,
.what-corner::before,
.what-content::after,
.what-corner::after {
  content: "";
  width: 10px;
  height: 10px;
  position: absolute;
  border: 4px solid #fff;
  opacity: 0;
  animation: whatCorners 0.2s 1 forwards;
  animation-delay: 0.5s;
}
@keyframes whatCorners {
  100% {
    opacity: 1;
  }
}
.what-content::before {
  top: -2px;
  left: -2px;
  border-right: none;
  border-bottom: none;
}
.what-corner::before {
  top: -2px;
  right: -2px;
  border-left: none;
  border-bottom: none;
}
.what-content::after {
  bottom: -2px;
  left: -2px;
  border-top: none;
  border-right: none;
}
.what-corner::after {
  bottom: -2px;
  right: -2px;
  border-left: none;
  border-top: none;
}
.what-arrow {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 25px;
  height: 25px;
  border: 1px solid #fff;
}
.what-arrow::before {
  content: "";
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  border-left: 1px solid #fff;
  border-bottom: 1px solid #fff;
  transition: 0.5s;
  width: 10px;
  height: 10px;
}
.what-noTouch:hover .what-arrow::before,
.what-content-active .what-arrow::before {
  transform: translate(-50%, -20%) rotate(-225deg);
}
.what-border1 {
  position: absolute;
  top: 0;
  width: 100%;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;
  transition: 0.5s;
  max-width: 0%;
  overflow: hidden;
  opacity: 0.8;
  animation: whatBorders 1.2s 1 forwards;
}
.what-border1 svg {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 450px;
}
.what-border2 {
  position: absolute;
  top: 50%;
  width: 100%;
  left: 0;
  transform: translateY(-50%);
  height: 100%;
  max-height: 0%;
  overflow: hidden;
  transition: 0.5s;
  opacity: 0.8;
  animation: whatBorders 1.2s 1 forwards;
}
.what-border2 svg {
  height: 500px;
  top: 50%;
  transform: translateY(-50%);
  position: absolute;
}
.what-content-in {
  height: 100%;
  overflow: hidden;
}
.what-content-in h5 {
  font-weight: 300;
  opacity: 0.5;
  font-size: 12px;
  letter-spacing: 1px;
  font-family: "Geist", sans-serif;
  margin-bottom: 5px;
}
@keyframes whatBorders {
  80% {
    opacity: 0.8;
  }
  100% {
    max-height: 100%;
    max-width: 100%;
    opacity: 0.2;
  }
}
.what-content-flex {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}
.what-tags {
  font-size: 13px;
  font-weight: 400;
  padding: 2px 7px;
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid #ffffff50;
  border-radius: 30px;
}
@media only screen and (max-width: 1600px) {
  .what-box h2 {
    margin-right: 18%;
  }
}
@media only screen and (max-width: 1400px) {
  .what-box h2 {
    margin-right: 20%;
  }
  .what-box-in {
    height: 400px;
  }
  .what-content h3 {
    font-size: 28px;
  }
  .what-content {
    padding: 30px;
    width: 400px;
  }
  .what-content p {
    font-size: 13px;
  }
  .what-noTouch:hover,
  .what-content-active {
    padding: 20px 30px;
  }

  .what-noTouch:hover ~ .what-content,
  .what-box-in:hover .what-noTouch:not(:hover),
  .what-content.what-sibling {
    padding: 10px 30px;
  }
  .what-tags {
    font-size: 12px;
  }
}
@media only screen and (max-width: 1400px) {
  .what-box-in {
    margin-left: 50px;
  }
  .what-content {
    width: 380px;
  }
}
@media only screen and (max-width: 1024px) {
  .whatIDO {
    height: auto;
    padding: 50px 0px;
    padding-bottom: 50px;
  }
  .what-box-in {
    height: 500px;
    margin-left: -50px;
  }
  .what-content {
    padding: 50px;
    width: 500px;
  }
  .what-content p {
    font-size: 14px;
  }
  .what-noTouch:hover,
  .what-content-active {
    min-height: 67%;
    padding: 50px;
  }

  .what-noTouch:hover ~ .what-content,
  .what-box-in:hover .what-noTouch:not(:hover),
  .what-content.what-sibling {
    min-height: 33%;
    padding: 10px 50px;
  }
}
@media only screen and (max-width: 900px) {
  .whatIDO {
    flex-direction: column;
  }
  .what-box h2 {
    margin: 50px 0;
    font-size: 55px;
    line-height: 53px;
  }
  .what-box:first-child {
    justify-content: left;
  }
  .what-box:last-child {
    height: 500px;
  }
  .what-box {
    width: 500px;
    max-width: calc(100% - 50px);
    margin: auto;
  }
  .what-content {
    width: 100%;
  }
  .what-box-in {
    margin-left: 0px;
    height: 450px;
  }
  .what-content h5,
  .what-content-flex {
    opacity: 0;
    transition: 0.3s;
  }
  .what-noTouch:hover h5,
  .what-content-active h5,
  .what-noTouch:hover .what-content-flex,
  .what-content-active .what-content-flex {
    opacity: 1;
  }
  .what-content {
    padding: 30px;
  }
  .what-content p {
    font-size: 11px;
  }
  .what-noTouch:hover,
  .what-content-active {
    padding: 10px 30px;
  }
  .what-tags {
    font-size: 11px;
  }

  .what-noTouch:hover ~ .what-content,
  .what-box-in:hover .what-noTouch:not(:hover),
  .what-content.what-sibling {
    padding: 5px 30px;
  }
  .what-content h3 {
    font-size: 25px;
  }
}
@media only screen and (max-width: 550px) {
  .whatIDO {
    place-items: inherit;
    align-items: start;
    justify-content: left;
  }
  .what-box {
    max-width: calc(100% - 25px);
    margin: 0;
  }
}
@media only screen and (min-width: 1950px) {
  .what-box h2 {
    font-size: 7rem;
    line-height: 6.8rem;
  }
}
