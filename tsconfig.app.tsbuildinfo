{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/about.tsx", "./src/components/career.tsx", "./src/components/contact.tsx", "./src/components/cursor.tsx", "./src/components/hoverlinks.tsx", "./src/components/landing.tsx", "./src/components/loading.tsx", "./src/components/maincontainer.tsx", "./src/components/navbar.tsx", "./src/components/socialicons.tsx", "./src/components/techstack.tsx", "./src/components/whatido.tsx", "./src/components/work.tsx", "./src/components/workimage.tsx", "./src/components/character/scene.tsx", "./src/components/character/exports.ts", "./src/components/character/index.tsx", "./src/components/character/utils/animationutils.ts", "./src/components/character/utils/character.ts", "./src/components/character/utils/decrypt.ts", "./src/components/character/utils/lighting.ts", "./src/components/character/utils/mouseutils.ts", "./src/components/character/utils/resizeutils.ts", "./src/components/utils/gsapscroll.ts", "./src/components/utils/initialfx.ts", "./src/components/utils/splittext.ts", "./src/context/loadingprovider.tsx", "./src/data/bonedata.ts"], "version": "5.6.2"}