@import url("https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap");

:root {
  font-family: "<PERSON>eist", sans-serif;

  font-optical-sizing: auto;
  font-style: normal;
  line-height: 1.5;
  scroll-behavior: smooth;
  color-scheme: light dark;
  color: #eae5ec;
  background-color: var(--backgroundColor);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  --accentColor: #c2a4ff;
  --backgroundColor: #0b080c;
  --vh: 100vh;
  --vh: 100svh;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Geist", sans-serif;
}
body {
  overflow: hidden;
}
a {
  color: inherit;
  text-decoration: inherit;
}
a:hover {
  color: var(--accentColor);
}
main {
  opacity: 1;
  transition: 0.3s;
}
.main-active {
  opacity: 0;
  animation: fadeIn 1s 1;
  animation-fill-mode: forwards;
}
@keyframes fadeIn {
  100% {
    opacity: 1;
  }
}
body {
  margin: 0;
  height: auto;
  background-color: #000;
  flex-grow: 1;
  --cWidth: calc(100% - 30px);
  --cMaxWidth: 1920px;
  max-width: 100vw;
  overflow-x: hidden;
}
.main-body {
  max-width: 100vw;
  overflow-x: hidden;
}

.container-main {
  width: 100%;
  margin: auto;
  position: relative;
}
.container1 {
  width: var(--cWidth);
  height: var(--vh);
  margin: auto;
  position: relative;
}
.split-line {
  overflow: hidden;
}
.split-h2 {
  overflow: hidden;
  display: flex;
  white-space: nowrap;
  flex-wrap: nowrap;
}

.techstack {
  width: 100%;
  position: relative;
  height: var(--vh);
  margin: auto;
  margin-top: 50px;
  margin-bottom: -100px;
}

.techstack h2 {
  font-size: 80px;
  text-align: center;
  position: absolute;
  width: 100%;
  top: 120px;
  left: 0;
  font-weight: 400;
  text-transform: uppercase;
}

@media screen and (min-width: 768px) {
  body {
    --cWidth: 94%;
  }
}
@media screen and (max-width: 900px) {
  .techstack h2 {
    font-size: 40px;
  }
}
